import { useState, useCallback } from 'react';
import { App } from 'antd';
import type { ModalStaticFunctions } from 'antd/es/modal/confirm';
import type { DataItem } from '../types/user';
import { UserService } from '../services/userService';

/**
 * 确认对话框配置类型
 */
type ConfirmConfig = {
  title: string;
  content: string;
  okText?: string;
  cancelText?: string;
  okType?: 'primary' | 'danger';
};

/**
 * 确认对话框Hook
 * 提供通用的确认对话框功能
 */
export const useConfirmDialog = () => {
  const [visible, setVisible] = useState(false);
  const [config, setConfig] = useState<ConfirmConfig>({
    title: '',
    content: '',
  });
  const [onConfirmCallback, setOnConfirmCallback] = useState<(() => void) | null>(null);

  /**
   * 显示确认对话框
   */
  const showConfirm = useCallback((confirmConfig: ConfirmConfig, onConfirm: () => void) => {
    setConfig(confirmConfig);
    setOnConfirmCallback(() => onConfirm);
    setVisible(true);
  }, []);

  /**
   * 确认操作
   */
  const handleConfirm = useCallback(() => {
    if (onConfirmCallback) {
      onConfirmCallback();
    }
    setVisible(false);
  }, [onConfirmCallback]);

  /**
   * 取消操作
   */
  const handleCancel = useCallback(() => {
    setVisible(false);
  }, []);

  return {
    visible,
    config,
    showConfirm,
    handleConfirm,
    handleCancel,
  };
};

/**
 * 用户删除确认Hook
 * 专门用于用户删除操作的确认对话框
 */
export const useUserDeleteConfirm = () => {
  const { modal, message } = App.useApp();
  const modalApi: Omit<ModalStaticFunctions, 'warn'> = modal;
  // 创建一个安全的 message 函数
  const showMessage = useCallback(
    (type: 'success' | 'error', content: string) => {
      message[type](content);
    },
    [message]
  );

  /**
   * 删除单个用户
   */
  const deleteUser = useCallback(
    async (record: DataItem, onSuccess?: () => void) => {
      try {
        const success = await UserService.deleteUser(record.id);
        if (success) {
          showMessage('success', `用户 ${record.name} 删除成功`);
          onSuccess?.();
        } else {
          throw new Error('删除失败');
        }
      } catch (error) {
        showMessage('error', `删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    },
    [showMessage]
  );

  /**
   * 批量删除用户
   */
  const batchDeleteUsers = useCallback(
    async (
      userIds: string[],
      _userNames: string[], // 使用下划线前缀表示未使用的参数
      onSuccess?: () => void
    ) => {
      try {
        const success = await UserService.batchDeleteUsers(userIds);
        if (success) {
          showMessage('success', `成功删除 ${userIds.length} 个用户`);
          onSuccess?.();
        } else {
          throw new Error('批量删除失败');
        }
      } catch (error) {
        showMessage('error', `批量删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    },
    [showMessage]
  );

  /**
   * 显示删除单个用户确认对话框
   */
  const showDeleteConfirm = useCallback(
    (record: DataItem, onSuccess?: () => void) => {
      modalApi.confirm({
        title: '确定要删除吗？',
        content: `确定要删除用户 "${record.name}" 吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        okType: 'danger',
        onOk: () => deleteUser(record, onSuccess),
      });
    },
    [deleteUser, modalApi]
  );

  /**
   * 显示批量删除确认对话框
   */
  const showBatchDeleteConfirm = useCallback(
    (selectedUsers: DataItem[], onSuccess?: () => void) => {
      const userIds = selectedUsers.map(user => user.id);
      const userNames = selectedUsers.map(user => user.name);

      // 构建内容字符串
      const contentText = `您即将删除以下 ${selectedUsers.length} 个用户：\n\n${userNames
        .map(name => `• ${name}`)
        .join('\n')}\n\n此操作不可恢复，请谨慎操作！`;

      modalApi.confirm({
        title: '批量删除确认',
        content: contentText,
        okText: '确定删除',
        cancelText: '取消',
        okType: 'danger',
        width: 400,
        onOk: () => batchDeleteUsers(userIds, userNames, onSuccess),
      });
    },
    [batchDeleteUsers, modalApi]
  );

  return {
    deleteUser,
    batchDeleteUsers,
    showDeleteConfirm,
    showBatchDeleteConfirm,
  };
};
