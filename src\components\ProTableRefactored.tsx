// 引入React基础库和hooks
import React, { useRef, useState, useCallback, useEffect } from 'react';
// 引入ProTable组件
import { ProTable } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';

// 引入Ant Design组件
import { Button, Space, Tag, InputNumber, Modal } from 'antd';
import { App } from 'antd';

// 引入图标
import { PlusOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons';

// 引入类型定义
import type { DataItem, SearchParams } from '../types/user';

// 引入服务
import { UserService } from '../services/userService';

// 引入自定义hooks
import { useTableSelection } from '../hooks/useTableSelection';
import { useModalState } from '../hooks/useModalState';
import { useUserForm } from '../hooks/useUserForm';
import { useUserDeleteConfirm } from '../hooks/useConfirmDialog';

// 引入组件
import { FormDrawer } from './common/FormDrawer';
import { UserForm } from './user/UserForm';
import { UserActions } from './user/UserActions';

/**
 * ProTable 重构后的组件
 * 使用hooks和组件化的方式实现数据表格功能
 */
const ProTableRefactored: React.FC = () => {
  // 获取 message 和 modal 实例
  const { message, modal } = App.useApp();

  // 表格引用，用于手动刷新表格
  const actionRef = useRef<ActionType>(null);
  const [currentRecord, setCurrentRecord] = useState<DataItem | null>(null);

  // 添加高度相关的状态
  const containerRef = useRef<HTMLDivElement>(null);
  const searchFormRef = useRef<HTMLDivElement>(null);
  const [tableScrollY, setTableScrollY] = useState<number>(700);

  // 使用自定义hooks
  const selection = useTableSelection();
  const editDrawer = useModalState();
  const addModal = useModalState();
  const userForm = useUserForm();
  const deleteConfirm = useUserDeleteConfirm();

  /**
   * 计算表格高度
   */
  const calculateTableHeight = useCallback(() => {
    if (!containerRef.current) return;

    try {
      // 获取容器总高度
      const containerHeight = containerRef.current.clientHeight;

      // 由于ProTable现在填满容器，需要从ProTable内部获取各部分高度
      const proTableElement = containerRef.current?.querySelector('.ant-pro-table');
      const searchForm = containerRef.current?.querySelector('.ant-pro-table-search');
      const tableAlert = containerRef.current?.querySelector('.ant-pro-table-alert');
      const toolbar = containerRef.current?.querySelector('.ant-pro-table-list-toolbar');
      const pagination = containerRef.current?.querySelector('.ant-pagination');

      // 计算各部分实际高度
      const searchFormHeight = searchForm?.clientHeight || 0;
      const tableAlertHeight = tableAlert?.clientHeight || 0;
      const toolbarHeight = toolbar?.clientHeight || 0;
      const paginationHeight = pagination?.clientHeight || 0;

      // 预留空间：表格头部、内边距等
      let reservedSpace = 110;
      if (tableAlert?.clientHeight) {
        reservedSpace = 120;
      }

      // 计算表格主体可用高度
      const availableHeight = containerHeight - searchFormHeight - tableAlertHeight - toolbarHeight - paginationHeight - reservedSpace;

      // 设置最小高度
      const minHeight = 300;
      const finalHeight = Math.max(availableHeight, minHeight);

      console.log('🔍 ProTable高度调试信息:', {
        容器总高度: containerHeight,
        ProTable总高度: proTableElement?.clientHeight || 0,
        查询表单高度: searchFormHeight,
        多选提示高度: tableAlertHeight,
        工具栏高度: toolbarHeight,
        分页器高度: paginationHeight,
        预留空间: reservedSpace,
        '计算的scroll.y': finalHeight,
        ProTable现在应该等于容器高度: containerHeight,
      });

      setTableScrollY(finalHeight);
    } catch (error) {
      console.error('计算表格高度失败:', error);
      setTableScrollY(700); // 降级到默认高度
    }
  }, [selection.hasSelection]);

  /**
   * 监听初始加载
   */
  useEffect(() => {
    // 立即计算，不延迟
    calculateTableHeight();

    // 初始加载时计算一次高度
    const timer = setTimeout(() => {
      calculateTableHeight();
    }, 50);

    return () => clearTimeout(timer);
  }, []); // 只在组件挂载时执行一次

  /**
   * 监听多选状态变化，重新计算高度
   */
  useEffect(() => {
    // 延迟计算，确保DOM更新完成
    const timer = setTimeout(() => {
      calculateTableHeight();
    }, 100);

    return () => clearTimeout(timer);
  }, [selection.selectedRowKeys.length, calculateTableHeight]); // 只在多选数量变化时执行

  /**
   * 监听浏览器窗口大小变化
   */
  useEffect(() => {
    const handleResize = () => {
      // 延迟计算，确保布局更新完成
      setTimeout(() => {
        calculateTableHeight();
      }, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [calculateTableHeight]);

  /**
   * 处理编辑操作
   */
  const handleEdit = (record: DataItem) => {
    setCurrentRecord(record);
    userForm.prepareEditForm(record);
    editDrawer.show();
  };

  /**
   * 处理编辑表单提交
   */
  const handleEditSubmit = async () => {
    if (!currentRecord) return;

    editDrawer.startLoading();
    const result = await userForm.submitEditForm(currentRecord.id);
    editDrawer.stopLoading();

    if (result.success) {
      editDrawer.hide();
      actionRef.current?.reload();
    }
  };

  /**
   * 处理抽屉关闭
   */
  const handleDrawerClose = () => {
    const cancelResult = userForm.handleFormCancel(() => {
      editDrawer.hide();
      userForm.resetForm(userForm.editForm);
    }, userForm.editForm);

    if (cancelResult.needConfirm) {
      modal.confirm({
        title: cancelResult.confirmTitle,
        content: cancelResult.confirmContent,
        onOk: cancelResult.onConfirm,
      });
    }
  };

  /**
   * 处理添加用户
   */
  const handleAddUser = () => {
    selection.clearSelection();
    addModal.show();
    userForm.resetForm(userForm.form);
  };

  /**
   * 处理添加用户提交
   */
  const handleAddSubmit = async () => {
    addModal.startLoading();
    const result = await userForm.submitAddForm();
    addModal.stopLoading();

    if (result.success) {
      addModal.hide();
      actionRef.current?.reload();
    }
  };

  /**
   * 处理添加用户取消
   */
  const handleAddCancel = () => {
    const cancelResult = userForm.handleFormCancel(() => {
      addModal.hide();
    }, userForm.form);

    if (cancelResult.needConfirm) {
      modal.confirm({
        title: cancelResult.confirmTitle,
        content: cancelResult.confirmContent,
        onOk: cancelResult.onConfirm,
      });
    } else {
      addModal.hide();
    }
  };

  /**
   * 处理删除操作
   */
  const handleDelete = (record: DataItem) => {
    deleteConfirm.deleteUser(record, () => {
      actionRef.current?.reload();
    });
  };

  /**
   * 处理批量删除
   */
  const handleBatchDelete = (selectedRows: DataItem[]) => {
    if (!selection.hasSelection) {
      return;
    }
    deleteConfirm.showBatchDeleteConfirm(selectedRows, () => {
      selection.clearSelection();
      actionRef.current?.reload();
    });
  };

  /**
   * 处理搜索请求
   */
  const handleSearch = async (params: SearchParams) => {
    try {
      const response = await UserService.getUsers(params);
      return {
        data: response.data,
        total: response.total,
        success: response.success,
      };
    } catch (error) {
      console.error('搜索失败:', error);
      return {
        data: [],
        total: 0,
        success: false,
      };
    }
  };

  /**
   * 表格列定义
   */
  const columns: ProColumns<DataItem>[] = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      ellipsis: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请输入姓名',
          },
        ],
      },
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      width: 80,
      valueType: 'digit',
      renderFormItem: () => {
        return (
          <Space.Compact
            style={{
              width: '100%',
            }}
          >
            <InputNumber style={{ flex: 1 }} placeholder='最小年龄' min={0} max={150} />
            <span
              style={{
                padding: '0 8px',
                lineHeight: '32px',
                color: '#999',
              }}
            >
              ~
            </span>
            <InputNumber style={{ flex: 1 }} placeholder='最大年龄' min={0} max={150} />
          </Space.Compact>
        );
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请输入年龄',
          },
        ],
      },
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      width: 200,
      ellipsis: true,
      search: false,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请输入地址',
          },
        ],
      },
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 150,
      search: false,
      render: (_, record) => (
        <Space>
          {record.tags?.map(tag => (
            <Tag key={tag} color='blue'>
              {tag}
            </Tag>
          ))}
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      valueType: 'dateTime',
      search: false,
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      search: false,
      render: (_, record) => <UserActions record={record} onEdit={handleEdit} onDelete={handleDelete} />,
    },
  ];

  return (
    <div
      ref={containerRef}
      style={{
        height: '100%', // 填满父容器
        overflow: 'hidden',
      }}
    >
      <ProTable<DataItem>
        columns={columns}
        actionRef={actionRef}
        request={handleSearch}
        rowKey='id'
        style={{
          height: '100%', // 让ProTable总高度等于父容器高度
          display: 'flex',
          flexDirection: 'column',
        }}
        search={{
          labelWidth: 'auto',
          defaultCollapsed: true,
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          optionRender: (_searchConfig, formProps, _dom) => [
            <div
              key='button-container'
              ref={searchFormRef}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                height: '32px',
                paddingTop: '4px',
              }}
            >
              <Button
                key='reset'
                icon={<ReloadOutlined />}
                onClick={() => {
                  formProps?.form?.resetFields();
                  selection.clearSelection();
                  actionRef.current?.reload();
                }}
                style={{
                  minWidth: 72,
                  width: 100,
                }}
              >
                重置
              </Button>
              <Button
                key='submit'
                type='primary'
                icon={<SearchOutlined />}
                htmlType='submit'
                onClick={() => {
                  selection.clearSelection();
                }}
                style={{
                  minWidth: 72,
                  width: 100,
                }}
              >
                查询
              </Button>
            </div>,
          ],
        }}
        pagination={{
          defaultPageSize: 50,
          showSizeChanger: true,
          showQuickJumper: true,
          onChange: (page, pageSize) => {
            console.log('分页变化:', {
              page,
              pageSize,
            });
            // 分页变化时重新计算高度
            setTimeout(() => {
              calculateTableHeight();
            }, 100);
          },
          onShowSizeChange: (current, size) => {
            console.log('页面大小变化:', { current, size });
            // 页面大小变化时重新计算高度
            setTimeout(() => {
              calculateTableHeight();
            }, 100);
          },
        }}
        dateFormatter='string'
        headerTitle={
          <Button type='primary' icon={<PlusOutlined />} onClick={handleAddUser}>
            添加用户
          </Button>
        }
        tableAlertRender={props => (
          <Space size={24}>
            <span>
              已选择{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {props.selectedRowKeys.length}
              </a>{' '}
              项
            </span>
          </Space>
        )}
        tableAlertOptionRender={props => (
          <Space size={16}>
            <Button type='link' danger size='small' onClick={() => handleBatchDelete(selection.selectedRows)}>
              批量删除
            </Button>
            <Button
              type='link'
              size='small'
              onClick={() => {
                props.onCleanSelected();
                selection.clearSelection();
              }}
            >
              清除选择
            </Button>
          </Space>
        )}
        toolBarRender={() => []}
        rowSelection={{
          selectedRowKeys: selection.selectedRowKeys,
          onChange: selection.handleSelectionChange,
          preserveSelectedRowKeys: true,
        }}
        scroll={{
          x: 1000,
          y: tableScrollY, // 使用动态计算的高度
        }}
        onLoad={() => {
          setTimeout(calculateTableHeight, 100);
        }}
      />

      <FormDrawer
        title='编辑用户信息'
        open={editDrawer.visible}
        onClose={handleDrawerClose}
        onSubmit={handleEditSubmit}
        onReset={() => userForm.resetForm(userForm.editForm)}
        loading={editDrawer.loading}
      >
        <UserForm
          form={userForm.editForm}
          initialValues={
            currentRecord
              ? {
                  name: currentRecord.name,
                  age: currentRecord.age,
                  address: currentRecord.address,
                  tags: currentRecord.tags,
                }
              : {}
          }
        />
      </FormDrawer>

      <Modal
        title='添加用户'
        open={addModal.visible}
        onOk={handleAddSubmit}
        onCancel={handleAddCancel}
        okText='确定'
        cancelText='取消'
        width={600}
        confirmLoading={addModal.loading}
        destroyOnClose
      >
        <UserForm form={userForm.form} style={{ marginTop: 16 }} />
      </Modal>
    </div>
  );
};

export default ProTableRefactored;
